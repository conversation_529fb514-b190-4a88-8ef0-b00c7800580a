"use client";

import { useState, useRef, useEffect, useContext } from "react";
import {
  Info,
  Upload,
  X,
  FileText,
  File,
  CircleChevronRight,
  CircleChevronLeft,
  AlertCircle,
  CodeXml
} from "lucide-react";

import {
  extractTextFromFile,
  getProjectFiles,
  fetchNodeById,
  getTopLevelRequirements,
  deleteProjectFile,
  getArchitecturalRequirementWithChildren,
  fetchSystemContextWithContainers,
  getAllComponentsFromProject,
  getInterfaceData,
} from "@/utils/api";
import {
  useParams,
  usePathname,
  useSearchParams,
  useRouter,
} from "next/navigation";
import { ProjectSetupContext } from "../Context/ProjectSetupContext";
import ProjectConfigurationStep from "./ProjectConfigurationStep";
import { DynamicButton } from "../UIComponents/Buttons/DynamicButton";
import RequirementConfigurationStep from "./RequirementConfigurationStep";
import EpicConfigurationStep from "./EpicConfigurationStep";
import ArchitecturalRequirementConfigurationStep from "./ArchitecturalRequirementConfiguration";
import SystemContextConfigurationStep from "./SystemContextConfiguration";
import { AlertContext } from "../NotificationAlertService/AlertList";
import ComponentConfigurationStep from "./ComponentConfigurationStep";
import InterfaceConfiguration from "./InterfaceConfiguration";
import DesignConfigurationStep from "./DesignConfiguration";
import en from "@/en.json";
import { useUser } from "@/components/Context/UserContext";
import { FaForward, FaInfoCircle } from "react-icons/fa";
import { ExecutionContext } from "../Context/ExecutionContext";

// Map of step names to their corresponding numbers

const STEP_NAME_MAP = {
  "Document Upload": 1,

  "Project Configuration": 2,

  "Requirement Configuration": 3,

  "User Stories": 4,

  "Architectural Requirements": 5,

  "System Architecture": 6,

  Containers: 7,

  "Components": 8,

  // "Components Details": 9,

  "Interfaces": 9,

  // "Interface Details": 10,

  "Designs": 10,

  // "Design Details": 11,
};

// Inverse mapping for step number to name

const STEP_NUMBER_MAP = Object.fromEntries(
  Object.entries(STEP_NAME_MAP).map(([name, number]) => [number, name])
);

export default function ProjectCreationFlow({
  onClose,
  initialStepName = null,
}) {
  const [files, setFiles] = useState([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);
  const pathname = usePathname();

  const { showAlert } = useContext(AlertContext);

  const [requirementData, setRequirementData] = useState([]);
  const searchParams = useSearchParams();
  const params = useParams();
  const router = useRouter();
  const { is_free_user } = useUser();

  const {
    projectId,
    setProjectId,
    setProjectData,
    projectData,
    hasProcessingFiles,
    setHasProcessingFiles,
    selectedContainerIdVal,
    isChecked
  } = useContext(ProjectSetupContext);
  const latestProjectId = projectId;
  const { configStatus, currentTaskId, setAutoNavigateEnabled } =
    useContext(ExecutionContext);
  const [taskStatus, setTaskStatus] = useState("Idle");
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenVal, setIsOpenVal] = useState(false);
  const [skippedEpics, setSkippedEpics] = useState(false);
  const [skippedReq, setSkippedReq] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [isAutoConfiguration, setIsAutoConfiguration] = useState(false);
  const [architectureReqDetails, setArchitecturalReqDetails] = useState(null);
  const [systemContexDetail, setSystemContextDetail] = useState(null)
  const [components, setComponents] = useState(null)
  const [interfaceData, setInterfaceData] = useState(null)
  const [isProjectConfig, setIsProjectConfig] = useState(false)
  const [isRequirementConfig, setIsRequirementConfig] = useState(false)
  const [enableDesignNode, setEnableDesignNode] = useState(false)
  const [autoConfigItems, setAutoConfigItems] = useState([]);
  const [isArchitectureReqConfig,setIsArchitectureReqConfig] = useState(false)


  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);
  const openSkipModal = () => setIsOpenVal(true);
  const closeSkipModal = () => setIsOpenVal(false);
  const openInfoModal = () => setShowInfo(true);
  const closeInfoModal = () => setShowInfo(false);

  const [step, setStep] = useState(() => {
    // Priority 1: If an initialStepName was provided (from direct URL navigation), use it
    if (initialStepName && STEP_NAME_MAP[initialStepName]) {
      return STEP_NAME_MAP[initialStepName];
    }

    // Priority 2: Check if stepName is in URL search params (manually entered)
    const stepNameParam = searchParams.get("stepName");
    if (stepNameParam && STEP_NAME_MAP[stepNameParam]) {
      return STEP_NAME_MAP[stepNameParam];
    }

    // Priority 3: Check localStorage for saved step for this project
    const savedStep = localStorage.getItem(
      `currentProjectStep-${latestProjectId}`
    );
    if (savedStep && parseInt(savedStep, 10) > 0) {
      return parseInt(savedStep, 10);
    }

    // Default to step 1 or 2 based on existing documents
    const hasDocuments = localStorage.getItem(
      `hasExistingDocuments-${latestProjectId}`
    );
    return hasDocuments ? 1 : 2;
  });

  useEffect(() => {
    if (latestProjectId) {
      localStorage.setItem(
        `currentProjectStep-${latestProjectId}`,
        step.toString()
      );
    }
  }, [step, latestProjectId]);

  // Replace localStorage usage with context
  const flag = sessionStorage.getItem(`openProjectContent-${latestProjectId}`);
  const hasDocuments = localStorage.getItem(
    `hasExistingDocuments-${latestProjectId}`
  );

  const steps = {
    1: {
      title: "Document Upload",
      description: en.DocumentUpload,
    },
    2: {
      title: "Project Configuration",
      description: en.ProjectConfiguration,
    },
    3: {
      title: "Requirement Configuration",
      description: en.RequirementConfiguration,
    },
    4: {
      title: "User Stories",
      description: en.UserStories,
    },
    5: {
      title: "Architectural Requirements",
      description: en.ArchitecturalRequirements,
    },
    6: {
      title: "System Architecture",
      description: en.SystemContextOverview,
    },
    7: {
      title: "Containers",
      description: en.SystemContextContainers,
    },
    // 8: {
    //   title: "Containers Details",
    //   description: en.ContainersDetails,
    // },
    8: {
      title: "Components",
      description: en.ListofComponents,
    },
    // 9: {
    //   title: "Components Details",
    //   description: en.ComponentsDetails,
    // },
    9: {
      title: "Interfaces",
      description: en.ListofInterfaces,
    },
    // 10: {
    //   title: "Interface Details",
    //   description: en.InterfaceDetails,
    // },
    10: {
      title: "Designs",
      description: en.ListofDesigns,
    },
    // 11: {
    //   title: "Design Details",
    //   description: en.DesignDetails,
    // },
  };

  useEffect(() => {
    if (step === 1) {
      fetchProjectFiles();
    }
  }, [step]);

  useEffect(() => {
    const openProjectModal = searchParams.get("openProjectModal");
    const urlStepName = searchParams.get("stepName");

    if (
      openProjectModal === "true" &&
      urlStepName &&
      STEP_NAME_MAP[urlStepName]
    ) {
      // If URL contains both parameters, set the step directly
      setStep(STEP_NAME_MAP[urlStepName]);
    }
  }, [searchParams]);

  useEffect(() => {
    const getProjectDetails = async () => {
      try {
        const response = await fetchNodeById(latestProjectId, "project");
        const requirements = await getTopLevelRequirements(latestProjectId);
        const archReq = await getArchitecturalRequirementWithChildren(
          latestProjectId
        );
        const systemContext = await fetchSystemContextWithContainers(
          latestProjectId
        );
        const componentContent = await getAllComponentsFromProject(
          latestProjectId
        );
        const interfaceData = await getInterfaceData(latestProjectId);
        setInterfaceData(interfaceData);
        setComponents(componentContent);
        setSystemContextDetail(systemContext);
        setArchitecturalReqDetails(archReq);
        setRequirementData(requirements);
        setProjectData(response);
      } catch (error) {

      }
    };

    getProjectDetails();
  }, [latestProjectId, searchParams, isProjectConfig,isRequirementConfig]);

  useEffect(() => {

    const refreshDataOnTaskComplete = async () => {
      if (taskStatus?.toLowerCase() === "complete") {
        try {
          const requirements = await getTopLevelRequirements(latestProjectId);
          setRequirementData(requirements);
          const response = await fetchNodeById(latestProjectId, "project");
          const archReq = await getArchitecturalRequirementWithChildren(latestProjectId);
          const systemContext = await fetchSystemContextWithContainers(latestProjectId);
          const componentContent = await getAllComponentsFromProject(latestProjectId);
          const interfaceData = await getInterfaceData(latestProjectId);

          setProjectData(response);
          setArchitecturalReqDetails(archReq);
          setSystemContextDetail(systemContext);
          setComponents(componentContent);
          setInterfaceData(interfaceData);
        } catch (error) {
        }
      }
    };

    refreshDataOnTaskComplete();
  }, [taskStatus, latestProjectId]);

  let filteredReqData = requirementData.filter(
    (item) =>
      item.id != null ||
      item.title != null ||
      item.type != null ||
      item.status != null ||
      item.assignee_name != null
  );

  useEffect(() => {
    if (step && STEP_NUMBER_MAP[step]) {
      // Store step in localStorage every time it changes
      localStorage.setItem(
        `currentProjectStep-${latestProjectId}`,
        step.toString()
      );

      // Optionally update URL for bookmarking purposes, but the primary state is in localStorage
      const newParams = new URLSearchParams(searchParams);
      newParams.set("stepName", STEP_NUMBER_MAP[step]);
      // Use replace to avoid adding to history stack
      router.replace(`${pathname}?${newParams.toString()}`, { scroll: false });
    }
  }, [step, pathname, router, searchParams, latestProjectId]);

  // ADD this reference for polling management at the top of your component,
  // after the state definitions
  const pollingIntervalRef = useRef(null);

  // ADD these polling management functions after fetchProjectFiles
  const startPolling = () => {
    // Clear any existing interval to avoid duplicates
    stopPolling();

    // Set up new polling interval
    pollingIntervalRef.current = setInterval(() => {
      fetchProjectFiles();
    }, 8000); // Poll every 8 seconds
  };

  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  // ADD this cleanup useEffect
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  // // REPLACE your existing fetchProjectFiles with this version
  // const fetchProjectFiles = async () => {
  //   if (!latestProjectId) return;

  //   try {
  //     const fileList = await getProjectFiles(latestProjectId);

  //     if (fileList && fileList.files) {
  //       // Check if any files are processing
  //       const hasProcessingDocs = fileList.files.some(file => file.status === "processing");
  //       setHasProcessingFiles(hasProcessingDocs);

  //       // Format files for state
  //       const formattedFiles = fileList.files.map((file) => {
  //         // Get progress dataGenerate Code
  //         const progressPercentage = file.progress?.percentage || file.percentage || 0;
  //         const elapsedTime = file.progress?.elapsed_time || 0;

  //         // Format elapsed time
  //         const formattedTime = elapsedTime
  //           ? `${Math.floor(elapsedTime / 60)}m ${Math.round(elapsedTime % 60)}s`
  //           : "";

  //         // Calculate remaining time if possible
  //         let remainingTime = "";
  //         if (file.progress?.timeout_value && progressPercentage > 0 && progressPercentage < 100) {
  //           const estimatedTotal = (elapsedTime / progressPercentage) * 100;
  //           const remaining = estimatedTotal - elapsedTime;
  //           remainingTime = `${Math.round(remaining)}s`;
  //         }

  //         // Find previous file to detect status changes
  //         const previousFile = files.find(f => f.name === file.filename);

  //         // Show alerts only for state transitions
  //         if (previousFile && previousFile.status === "processing" && file.status !== "processing") {
  //           if (file.status === "completed") {
  //             showAlert("Document processed successfully", "success");
  //           } else if (file.status === "failed") {
  //             showAlert("Failed to process document", "error");
  //           }
  //         }

  //         return {
  //           id: previousFile?.id || Date.now(),
  //           name: file.filename,
  //           file_uuid: file.file_uuid,
  //           type: file.filename.includes("pdf")
  //             ? "PDF"
  //             : file.filename.split(".").pop().toUpperCase(),
  //           size: formatBytes(file.size),
  //           author: file.uploaded_by || "Unknown",
  //           tags: file.tags || ["document"],
  //           status: file.status,
  //           progress: progressPercentage,
  //           percentage: progressPercentage,
  //           elapsedTime: formattedTime,
  //           remainingTime: remainingTime,
  //         };
  //       });

  //       setFiles(formattedFiles);

  //       // Start/continue polling if there are processing files
  //       if (hasProcessingDocs) {
  //         startPolling();
  //       } else {
  //         stopPolling();
  //       }
  //     }
  //   } catch (err) {
  //
  //   }
  // };

  const fetchProjectFiles = async () => {
    if (!latestProjectId) return;

    try {
      const fileList = await getProjectFiles(latestProjectId);

      if (fileList && fileList.files) {
        // Check if any files are processing
        const hasProcessingDocs = fileList.files.some(
          (file) => file.status === "processing"
        );
        setHasProcessingFiles(hasProcessingDocs);

        // Format files for state
        const formattedFiles = fileList.files.map((file) => {
          // Get progress data
          const progressPercentage =
            file.progress?.percentage || file.percentage || 0;
          const elapsedTime = file.progress?.elapsed_time || 0;

          // Format elapsed time
          const formattedTime = elapsedTime
            ? `${Math.floor(elapsedTime / 60)}m ${Math.round(
              elapsedTime % 60
            )}s`
            : "";

          // Calculate remaining time if possible
          let remainingTime = "";
          if (
            file.progress?.timeout_value &&
            progressPercentage > 0 &&
            progressPercentage < 100
          ) {
            const estimatedTotal = (elapsedTime / progressPercentage) * 100;
            const remaining = estimatedTotal - elapsedTime;
            remainingTime = `${Math.round(remaining)}s`;
          }

          // Find previous file in our state to detect status changes
          const previousFile = files.find((f) => f.name === file.filename);

          // Important: Show alerts only for status transitions
          if (previousFile && previousFile.status === "processing") {
            if (file.status === "completed") {
              // showAlert(
              //   `Document ${file.filename} processed successfully`,
              //   "success"
              // );
            } else if (file.status === "failed") {
              showAlert(`Failed to process document ${file.filename}`, "error");
            }
          }

          return {
            id: previousFile?.id || Date.now(),
            name: file.filename,
            file_uuid: file.file_uuid,
            type: file.filename.includes("pdf")
              ? "PDF"
              : file.filename.split(".").pop().toUpperCase(),
            size: formatBytes(file.size),
            author: file.uploaded_by || "Unknown",
            tags: file.tags || ["document"],
            status: file.status,
            progress: progressPercentage,
            percentage: progressPercentage,
            elapsedTime: formattedTime,
            remainingTime: remainingTime,
          };
        });

        setFiles(formattedFiles);

        // Start/continue polling if there are processing files
        if (hasProcessingDocs) {
          startPolling();
        } else {
          stopPolling();
        }
      }
    } catch (err) {

    }
  };
  // useEffect(() => {
  //   if (!hasDocuments && step === 1) {
  //     setStep(2);
  //   }
  // }, [hasDocuments, step]);

  const handleNext = () => {
    if (step === 8 || step === 7) {
      const newParams = new URLSearchParams(searchParams);
      if (newParams.has("containerid")) {
        newParams.delete("containerid");
      }
      if (newParams.has("GenerateCode")) {
        newParams.delete("GenerateCode");
      }
      router.replace(`${pathname}?${newParams.toString()}`, { scroll: false });
      setStep(step + 1)
    }
    else if (step < Object.keys(steps).length) {
      setStep(step + 1)
    }

  };


  const handleBack = () => {
    if (step === 6 || step === 7 || step === 8) {
      const newParams = new URLSearchParams(searchParams);
      if (newParams.has("containerid")) {
        newParams.delete("containerid");
      }
      if (newParams.has("GenerateCode")) {
        newParams.delete("GenerateCode");
      }
      router.replace(`${pathname}?${newParams.toString()}`, { scroll: false });
      setStep((prev) => prev - 1);
    }
    else if (step === 5 && skippedEpics) {
      setStep(step - 2);
    } else if (step === 5 && skippedReq) {
      setStep(step - 3);
    } 
    else if (step > 2 || (step > 1)) {
      setStep((prev) => prev - 1);
    }


  };
  const handleSkip = () => {
    if (step === 3) {
      setStep(step + 2);
      setSkippedReq(true);
    }
    if (step === 5) {
      setStep(step + 1);
    }
    setIsOpenVal(false);
  };

  const handleSkipAllEpics = () => {
    if (step === 4) {
      setStep(step + 1);
      setSkippedEpics(true);
    }
  };

  const handleClose = () => {
    // Save current step to localStorage before closing
    if (latestProjectId) {
      // Save the current step so it will be restored next time the modal opens
      // regardless of URL changes or page refreshes
      localStorage.setItem(
        `currentProjectStep-${latestProjectId}`,
        step.toString()
      );

      // Also save the step name for easier debugging
      if (STEP_NUMBER_MAP[step]) {
        localStorage.setItem(
          `currentProjectStepName-${latestProjectId}`,
          STEP_NUMBER_MAP[step]
        );
      }

      // Clean up URL parameters if present (optional)
      const newParams = new URLSearchParams(searchParams);

      if (newParams.has("stepName")) {
        newParams.delete("stepName");
      }

      if (newParams.has("openProjectModal")) {
        newParams.delete("openProjectModal");
      }
      if (newParams.has("selectedEpicId")) {
        newParams.delete("selectedEpicId");
      }
      if (newParams.has("selectedContainerId")) {
        newParams.delete("selectedContainerId");
      }
      if (newParams.has("selectedComponentId")) {
        newParams.delete("selectedComponentId");
      }
      if (newParams.has("selectedInterfaceId")) {
        newParams.delete("selectedInterfaceId");
      }
      if (newParams.has("selectedDesignId")) {
        newParams.delete("selectedDesignId");
      }
      if (newParams.has("containerid")) {
        newParams.delete("containerid");
      }
      if (newParams.has("GenerateCode")) {
        newParams.delete("GenerateCode");
      }
      router.replace(`${pathname}?${newParams.toString()}`, { scroll: false });
    }

    // Call the original onClose
    onClose();
  };

  useEffect(() => {
    const openProjectModal = searchParams.get("openProjectModal");
    const urlStepName = searchParams.get("stepName");
    const selectedEpicId = searchParams.get("selectedEpicId");
    const selectedContainerId = searchParams.get("selectedContainerId");
    const selectedComponentId = searchParams.get("selectedComponentId");
    const selectedInterfaceId = searchParams.get("selectedInterfaceId");
    const selectedDesignId = searchParams.get("selectedDesignId");

    if (
      openProjectModal === "true" &&
      urlStepName &&
      STEP_NAME_MAP[urlStepName]
    ) {
      // If URL contains both parameters, set the step directly
      setStep(STEP_NAME_MAP[urlStepName]);
    } else if (selectedEpicId && urlStepName === "User Stories") {

      setStep(4);
    }
    else if (selectedContainerId && urlStepName === "Containers") {

      setStep(7);
    } else if (selectedComponentId && urlStepName === "Components") {
      setStep(8);
    } else if (selectedInterfaceId && urlStepName === "Interfaces") {

      setStep(9);
    } else if (selectedDesignId && urlStepName === "Designs") {

      setStep(10);
    }
  }, [searchParams]);

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFileUpload(droppedFiles);
  };

  const handleBrowseFiles = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const selectedFiles = Array.from(e.target.files);
    handleFileUpload(selectedFiles);
  };

  // REPLACE your existing handleFileUpload with this version
  const handleFileUpload = async (uploadedFiles) => {
    const newFiles = uploadedFiles.map((file, i) => ({
      id: Date.now() + i,
      name: file.name,
      type: file.type.includes("pdf") ? "PDF" : file.type,
      size: formatBytes(file.size),
      author: "Unknown",
      tags: ["document"],
      status: "processing",
      progress: 0,
      percentage: 0,
      fileObject: file,
    }));

    // Update state with new files
    setFiles((prev) => [...prev, ...newFiles]);
    setHasProcessingFiles(true);

    // Start polling as we have processing files now
    startPolling();

    // Upload each file
    for (const doc of newFiles) {
      try {
        await extractTextFromFile(latestProjectId, [doc.fileObject]);
        // Don't update status here - let the polling handle it
      } catch (err) {

        setFiles((prev) =>
          prev.map((f) =>
            f.id === doc.id
              ? { ...f, status: "error", progress: 0, percentage: 0 }
              : f
          )
        );
        showAlert(
          `Failed to upload ${doc.name}: ${err.message || "Unknown error"}`,
          "error"
        );
      }
    }
  };

  const handleGenerateCodeClick = () => {
    const searchParams = new URLSearchParams({
      containerid: selectedContainerIdVal,
      GenerateCode: 'true',
    });
    router.push(`?${searchParams.toString()}`);
  };

  const formatBytes = (bytes, decimals = 2) => {
    if (!+bytes) return "0 Bytes";
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
  };

  // MODIFY your removeFile function to use the new polling system
  const removeFile = async (id) => {
    try {
      const response = await deleteProjectFile(latestProjectId, id);
      if (response.status === 200) {
        showAlert("Document removed successfully.", "success");
        // Fetch files again after deletion
        fetchProjectFiles();
      } else {
        showAlert("Failed to remove the document.", "danger");
      }
    } catch (error) {

    }
  };
useEffect(()=>{
    if (configStatus[currentTaskId]) {
    
      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
     
    }
  },[currentTaskId, configStatus[currentTaskId],projectId])

 const shouldDisableButton = (step) => {
    // if (is_free_user && step >= 2) {
    //   console.error("is_free_user inside if",is_free_user)
    //   return true;
    // }
    if (taskStatus.toLowerCase() === "in_progress") {
      return true; // Disable button when task is in progress
    }

    switch (step) {
      case 1:
        return hasProcessingFiles;
      case 2:
        const project_flag =
          projectData?.properties?.configuration_state === "configured" ||
          isProjectConfig;
        return !project_flag;
      case 3:
        return !isRequirementConfig;
      case 5:
        if(isArchitectureReqConfig){
          return false
        }
        const architecture_flag =
          architectureReqDetails?.properties?.configuration_state ===
          "configured";


        return !architecture_flag;
      case 6:
        const systemContextOverview_flag =
          systemContexDetail?.data?.systemContext?.properties
            ?.overview_config_state === "configured" && systemContexDetail?.data?.systemContext?.properties?.containers_config_state === "configured";
        return !systemContextOverview_flag;
      case 7:
        const systemContextContainers_flag =
          systemContexDetail?.data?.containers?.every(
            (container) =>
              container?.properties?.configuration_state === "configured"
          );
        return !systemContextContainers_flag;

      case 8:
        const components_overview_flag = components?.containers?.every(container =>
          container.components?.every(component =>
            component.properties?.configuration_state === 'configured'
          )
        );
        return !components_overview_flag;

      case 9:
        if (!interfaceData || interfaceData[0].interfaces.length === 0) return false;

        const interfaces_detail_flag = interfaceData[0]?.interfaces?.every(interfaceItem => {
          const child = interfaceItem.child_node;
          return (
            child?.properties?.definition_state === "configured" && child?.properties?.design_details_state === "configured"
          );
        });
        return !interfaces_detail_flag;
      case 10:
        const design_detail_flag = enableDesignNode;
        return true;

      default:
        return false; // Enable button by default
    }
  };
  function CloseConfirmModel() {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={closeModal}>
        <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b">
            <h2 className="typography-body-lg font-weight-semibold">Close Project Guide Setup</h2>
          </div>

          {/* Body */}
          <div className="p-4">
            <p className="text-gray-600 typography-body-sm">
              Are you sure you want to close the project guide setup? Any
              unsaved changes will be lost.
            </p>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-2 p-4 border-t">
            <button
              onClick={closeModal}
              className="px-4 py-2 typography-body text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleClose}
              className="px-4 py-2 typography-body bg-red-500 text-white rounded-md hover:bg-red-600"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  function SkipModel() {
    const isStepThree = step === 3;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={closeSkipModal}>
        <div className="bg-white rounded-lg shadow-lg w-full max-w-md">

          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b">
            <h2 className="typography-body-lg font-weight-semibold">Skip Configuration</h2>
          </div>

          {/* Body */}
          <div className="p-4 space-y-3">
            <p className="text-gray-700 typography-body-sm">
              Are you sure you want to skip the current {isStepThree ? "Requirement" : "Architectural Requirement"} configuration?
            </p>

            <div className="flex items-start gap-2 bg-blue-50 border border-blue-200 text-blue-800 typography-body-sm rounded-md p-3">
              <svg className="w-5 h-5 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M12 20c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z" />
              </svg>
              <p>
                You will proceed directly to the <strong>{isStepThree ? "Architectural Requirements" : "System Context"}</strong> section. You can always create {isStepThree ? "Requirement" : "Architecture Requirement"} later under the {isStepThree ? "Requirements" : "Architecture"}  section.
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-2 p-4 border-t">
            <button
              onClick={closeSkipModal}
              className="px-4 py-2 typography-body text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleSkip}
              className="px-4 py-2 typography-body bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Skip
            </button>
          </div>
        </div>
      </div>
    );
  }

  function ModalGuidance() {
    const getTitleForStep = (step) => {
      switch (step) {
        case 2:
          return "Required Step: Project Configuration";
        case 6:
          return "Required Step: System Context";
        case 7:
          return "Required Step: Containers";
        case 9:
          return "Required Step: Interface"
        case 10:
          return "Required Step: Design"
        default:
          return "Important Information";
      }
    };

    const getDescriptionForStep = (step) => {
      switch (step) {
        case 2:
          return {
            primary: {
              "Purpose": "Ensures that your project's key settings and attributes are properly defined.",
              "Importance": "Lays the foundation for accurate and effective system design."
            }
          };
        case 6:
          return {
            primary: {
              "Purpose": "Clarifies how your system interacts with external entities.",
              "Importance": "Essential for understanding integration points and dependencies."
            }
          };
        case 7:
          return {
            primary: {
              "Purpose": "Defines the main components of your system.",
              "Importance": "Supports maintainability and scalability."
            }
          };
        case 9:
          return {
            primary: {
              "Purpose": "Defines how system components and external systems communicate.",
              "Importance": "Ensures compatibility, interoperability, and a clear contract between components."
            }
          };

        case 10:
          return {
            primary: {
              "Purpose": "Establishes the detailed internal structure and interactions of system components.",
              "Importance": "Critical for ensuring coherent design, performance, and long-term extensibility."
            }
          };
        default:
          return {
            primary: {
              "Note": "This step contains essential information to guide your system design."
            }
          };
      }
    };

    const { primary } = getDescriptionForStep(step);

    return (
      <div className="relative">
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={closeInfoModal}>
          <div className="bg-white rounded-lg p-6 max-w-md w-full shadow-xl">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <Info className="text-blue-600" size={24} />
                </div>
                <h2 className="typography-heading-4 font-weight-bold text-gray-800">
                  {getTitleForStep(step)}
                </h2>
              </div>

              <button
                onClick={closeInfoModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <hr className="mb-4 border-gray-300" />

            <div className="mb-6 space-y-2">
              {Object.entries(primary).map(([key, value]) => (
                <p key={key} className="text-gray-700">
                  <span className="font-weight-semibold">{key}:</span> {value}
                </p>
              ))}
            </div>

            <div className="mt-4 p-4 bg-amber-100 border-l-4 border-amber-400 rounded-md flex items-center">
              <AlertCircle className="text-amber-600 mr-3" size={24} />
              <p className="text-amber-800 typography-body-sm font-weight-medium">
                You must complete this step before proceeding
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

const shouldDisableSkipButton = () => {
  if (step === 3) {
    return filteredReqData.length > 0 || isAutoConfiguration;
  }
  if (step === 5) {
    return isAutoConfiguration || architectureReqDetails?.properties?.configuration_state === "configured";
  }
  return false;
};


  const isDisabled = shouldDisableButton(step);
  // const isDisabled = shouldDisableButton(true);

  const isStepLocked = (stepNum) => {
    return is_free_user && stepNum > 2;
  };
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm ">
      <div className="w-full  min-w-[400px] max-w-[90vw] h-[90vh] bg-white dark:bg-gray-800 rounded-lg shadow-lg flex flex-col">
        {/* Header */}

        <div className="m-4 flex items-center">
          <div className="w-10">{/* Empty div for spacing */}</div>
          <div className="flex-1 flex justify-center">
            <h2 className="typography-heading-4 font-weight-semibold">
              {projectData?.properties?.Title || "Create New Project"}
            </h2>
          </div>
          <div className="w-10 flex justify-end">
            <DynamicButton
              variant="ghost"
              icon={X}
              onClick={openModal}
              tooltip="Close"
            />
          </div>
        </div>

        {/* Progress bar (standalone) */}
        <div className="px-4 mb-3">
          <div className="flex justify-between">
            {Object.entries(steps).map(([stepNum, stepData]) => {
              const stepNumber = parseInt(stepNum);
              const isCurrentStep = stepNumber === step;
              const isCompleted = stepNumber < step;
              const isFirst = stepNumber === 1;
              const isClickable = isCompleted && !isFirst;

              return (
                <div
                  key={stepNumber}
                  className={`relative flex-1 mx-0.5 group ${isCurrentStep ? "z-10" : ""
                    }`}
                >
                  {/* Progress bar with enhanced current step visibility */}
                  <div
                    className={`relative h-2 rounded-full transition-all duration-300 ${isClickable ? "cursor-pointer" : "cursor-default"
                      } ${isCurrentStep
                        ? "bg-orange-400"
                        : isCompleted
                          ? "bg-orange-400"
                          : "bg-gray-200"
                      }`}
                    onClick={() => (isClickable ? setStep(stepNumber) : null)}
                  >
                    {/* Additional visual indicator for current step */}
                    {isCurrentStep && (
                      <div
                        className="absolute inset-0 rounded-full"
                        style={{
                          boxShadow: "0 0 0 2px rgba(251, 146, 60, 0.5)",
                          animation: "2s infinite subtle-pulse-border",
                          animationTimingFunction: "ease-in-out",
                        }}
                      />
                    )}
                  </div>

                  {isCurrentStep && (
                    <style>{`
    @keyframes subtle-pulse-border {
      0% { box-shadow: 0 0 0 2px rgba(251, 146, 60, 0.4); }
      50% { box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2); }
      100% { box-shadow: 0 0 0 2px rgba(251, 146, 60, 0.4); }
    }
  `}</style>
                  )}

                  {/* Improved tooltip with more space */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                    <div className="bg-gray-800 text-white typography-caption rounded py-1 px-2 whitespace-nowrap">
                      {stepData.title}
                    </div>
                    <div className="w-2 h-2 bg-gray-800 transform rotate-45 absolute left-1/2 -translate-x-1/2 -bottom-1"></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Step information in banner */}
        <div className="mx-4 mb-3 mt-2">
          <div className="bg-orange-50 border-l-4 border-orange-500 px-4 py-3 rounded-r-md w-full">
            <div className="flex flex-col">
              <div className="flex items-center">
                <h3 className="typography-body-lg font-weight-medium text-gray-800">
                  Step {step} of {Object.keys(steps).length}:
                </h3>
                <span className="ml-2 typography-body-lg font-weight-semibold text-gray-900">
                  {steps[step]?.title}
                </span>
              </div>
              <p className="typography-body text-gray-600 mt-1">
                {steps[step]?.description}
              </p>
              {step === 2 && (
                <span className="text-gray-600 mb-2 -mt-1 typography-body">
                  {en.ProjectConfigurationSub}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Progress indicator */}

        {/* Content */}
        <div className="flex-1 px-8 py-4 flex flex-col overflow-hidden">
          <div className="flex items-center mb-6">
            <div className="flex-1 flex justify-between items-center">
              {((hasDocuments && step === 1) ||(step ===1) ) ? (
                <div className="w-[160px]"></div> // Adjust width to match actual button size
              ) : (
                <button
                  className="flex items-center px-6 py-2 rounded-md bg-white text-black border border-gray-200 hover:bg-orange-600 hover:text-white transition-colors duration-200"
                  onClick={handleBack}
                  title="Go to previous one"
                  disabled={step === 1}
                >
                  <CircleChevronLeft className="w-5 h-5 mr-1" />
                  <span>Back</span>
                </button>
              )}
              <div className="flex items-center space-x-3">
                {step === 5 || step === 3 ? (
                  <button
                    className={`flex items-center px-6 py-2 rounded-md bg-white text-black border border-gray-200 hover:bg-orange-600 hover:text-white transition-colors duration-200  disabled:opacity-50 disabled:cursor-not-allowed`}
                    onClick={handleSkip}
                    title={"This is an optional section you may skip"}
                    disabled={shouldDisableSkipButton()}
                  >
                    <FaForward size={16} className="mr-1" />
                    Skip
                  </button>
                ) : null}

                {/* Blinking Info Button */}
                {(step === 2 || step === 6 || step === 7 || step === 9 || step === 10) ? (
                  <button
                    className="relative p-2 rounded-full bg-yellow-100 text-yellow-700 hover:bg-yellow-200 transition-all duration-300"
                    title="Click to learn more about this step"
                    style={{
                      animation: "blinkButton 2s infinite"
                    }}
                    onClick={openInfoModal}
                  >
                    <FaInfoCircle size={18} />
                  </button>
                ) : null}

                {(step === 7) ? (
                  <DynamicButton
                    type="submit"
                    size="medium"
                    icon={CodeXml}
                    variant="primaryLegacy"
                    label="Generate Code"
                    onClick={handleGenerateCodeClick}
                    text="Generate Code"
                    className="bg-primary-50 text-primary-600 hover:bg-primary-100"
                    tooltip={isChecked ? "Click to generate the code" : "click the internal container from the table to generate code"}
                    disable={!isChecked}
                  />
                ) : null}

                {step !== 10 && (
                  <button
                    className={`flex items-center px-6 py-2 rounded-md text-white ${isDisabled
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-orange-500 hover:bg-orange-600"
                      }`}
                    onClick={handleNext}
                    title={
                      isDisabled
                        ? "Finish this section before moving forward"
                        : `Proceed to ${STEP_NUMBER_MAP[step + 1] || "next step"}`
                    }
                    disabled={isDisabled}
                    style={
                      !isDisabled
                        ? {
                          animation: "blinkButton 2s infinite",
                        }
                        : {}
                    }
                  >
                    <CircleChevronRight className="w-5 h-5 ml-2 mr-2" />
                    {/* Dynamic text showing "Continue to [next step name]" */}
                    <span className="mr-1">
                      Continue to{" "}
                      <span>{STEP_NUMBER_MAP[step + 1] || "next step"}</span>
                    </span>
                    {/* <CircleChevronRight className="w-5 h-5" /> */}

                    {/* Inline style for blinking button animation - only added when button is enabled */}
                    {!isDisabled && (
                      <style>{`
      @keyframes blinkButton {
        0% { box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.7); }
        50% { box-shadow: 0 0 0 8px rgba(249, 115, 22, 0); }
        100% { box-shadow: 0 0 0 0 rgba(249, 115, 22, 0); }
      }
    `}</style>
                    )}
                  </button>)}
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-auto">
            {step === 1 && (
              <>
                {/* Upload area */}
                <div
                  className={`border-2 border-dashed rounded-lg flex-1 flex flex-col items-center justify-center p-8 mb-6 ${isDragging
                      ? "border-orange-500 bg-orange-50"
                      : "border-gray-300"
                    }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                    <Upload className="w-6 h-6 text-orange-500" />
                  </div>
                  <p className="text-gray-700 text-center mb-2">
                    Drag and drop files here or click to browse
                  </p>
                  <button
                    onClick={handleBrowseFiles}
                    className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 mt-4"
                  >
                    Browse Files
                  </button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                    multiple
                  />
                </div>

                {/* File list */}
                {files.length > 0 && (
                  <div className="h-full ">
                    <div className="space-y-3">
                      {files.map((file, index) => (
                        <div
                          key={index}
                          className="flex flex-col p-3 bg-gray-50 border rounded-md"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className="mr-3">
                                {file.type === "pdf" ? (
                                  <FileText className="w-6 h-6 text-orange-500" />
                                ) : (
                                  <File className="w-6 h-6 text-blue-500" />
                                )}
                              </div>
                              <div>
                                <p className="font-weight-medium">{file.name}</p>
                                <div className="flex items-center gap-2 typography-body-sm text-gray-500">
                                  <span>{file.size}</span>
                                  {file.status && (
                                    <>
                                      <span>•</span>
                                      <span
                                        className={`${file.status === "processing"
                                            ? "text-blue-600"
                                            : file.status === "completed"
                                              ? "text-green-600"
                                              : "text-red-600"
                                          }`}
                                      >
                                        {file.status}
                                      </span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => removeFile(file.file_uuid)}
                              className="text-gray-500 hover:text-gray-700"
                              disabled={file.status === "processing"}
                            >
                              <X className="w-5 h-5" />
                            </button>
                          </div>

                          {/* Progress display */}
                          {file.status && (
                            <>
                              {/* Replace the progress display section in the file list rendering */}
                              {file.status === "processing" && (
                                <div className="mt-2">
                                  <div className="flex justify-between items-center mb-1">
                                    <span className="typography-body-sm text-gray-500">
                                      Processing...
                                    </span>
                                    <span className="typography-body-sm font-weight-medium text-blue-600">
                                      {Math.round(file.progress)}%
                                    </span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                      className="h-2 rounded-full bg-blue-500 transition-all duration-500"
                                      style={{
                                        width: `${file.progress}%`,
                                        animation:
                                          "progress-pulse 1.5s ease-in-out infinite",
                                      }}
                                    />
                                  </div>
                                  {/* Remaining time */}
                                  {file.remainingTime && (
                                    <div className="mt-1 text-right">
                                      <span className="typography-caption text-gray-600">
                                        Estimated time remaining:{" "}
                                        {file.remainingTime}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              )}
                              {file.status !== "processing" && (
                                <div className="mt-2">
                                  <div className="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                      className={`h-2 rounded-full ${file.status === "completed"
                                          ? "bg-green-500"
                                          : "bg-red-500"
                                        }`}
                                      style={{ width: "100%" }}
                                    />
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}

            {step === 2 && (
              <div className="h-full">
                <ProjectConfigurationStep
                  projectData={projectData}
                  onClose={onClose}
                  setIsProjectConfig={setIsProjectConfig}
                  setAutoConfigItems={setAutoConfigItems}
                />
              </div>
            )}
            {step === 3 && (
              <div className="h-full">
                <RequirementConfigurationStep
                  requirementData={requirementData}
                  setIsRequirementConfig={setIsRequirementConfig}
                  setIsAutoConfiguration={setIsAutoConfiguration}
                  autoConfigItems={autoConfigItems}
                />
              </div>
            )}
            {step === 4 && (
              <div className="h-full">
                <EpicConfigurationStep
                  handleSkipAllEpics={handleSkipAllEpics}
                />
              </div>
            )}
            {step === 5 && (
              <div className="h-full">
                <ArchitecturalRequirementConfigurationStep 
                setIsAutoConfiguration={setIsAutoConfiguration}
                setIsArchitectureReqConfig={setIsArchitectureReqConfig}
                />
              </div>
            )}
            {step === 6 && (
              <div className="h-full">
                <SystemContextConfigurationStep type="overview" />
              </div>
            )}
            {step === 7 && (
              <div className="h-full">
                <SystemContextConfigurationStep type="container" />
              </div>
            )}
            {/* {step === 8 && (
              <div className="h-full">
                <ContainerConfigurationStep />
              </div>
            )} */}
            {step === 8 && (
              <div className="h-full">
                <ComponentConfigurationStep type="componentsList" />
              </div>
            )}
            {/* {step === 9 && (
              <div className="h-full">
                <ComponentConfigurationStep type="components overview" />
              </div>
            )} */}
            {step === 9 && (
              <div className="h-full">
                <InterfaceConfiguration type="InterfaceList" />
              </div>
            )}
            {/* {step === 10 && (
              <div className="h-full">
                <InterfaceConfiguration type="Interface Overview" />
              </div>
            )} */}
            {step === 10 && (
              <div className="h-full">
                <DesignConfigurationStep type="design list" setEnableDesignNode={setEnableDesignNode} />
              </div>
            )}
            {/* {step === 11 && (
              <div className="h-full">
                <DesignConfigurationStep />
              </div>
            )} */}
          </div>
        </div>
      </div>
      {isOpen ? <CloseConfirmModel /> : null}
      {isOpenVal ? <SkipModel /> : null}
      {showInfo ? <ModalGuidance /> : null}
    </div>
  );
}
